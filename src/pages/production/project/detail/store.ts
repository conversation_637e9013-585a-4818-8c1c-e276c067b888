import { create } from 'zustand'
import { post, get as requestGet, envUrl } from '../../../../utils/request'
import type { BudgetCategory, BudgetSubcategory } from '../../../../consts/budget'

// 预算类目信息（对应API的PrProductionBudgetItems）
export interface IPrProductionBudgetItem {
  id?: number // 主键ID
  productionId: number // 关联项目ID
  isPackage?: string // 是否打包
  itemName?: string // 打包名称
  quotedPrice?: number // 单价
  personCount: number // 人数
  dayCount: number // 拍摄天数
  totalPrice?: number // 总金额
  hasInvoice?: boolean // 是否正规发票（1=是，0=否）
  description?: string // 备注说明
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
  itemsDetail?: IPrProductionItemsDetail[] // 打包子类列表
}

// 打包子类信息（对应API的PrProductionItemsDetail）
export interface IPrProductionItemsDetail {
  id?: number // 主键ID
  itemId: number // 打包项Id
  category: BudgetCategory // 一级分类
  subcategory: BudgetSubcategory // 二级分类
  personCount: number // 人数
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
  categoryLabel?: string // 一级分类标签（只读）
  subcategoryLabel?: string // 二级分类标签（只读）
}

// 预算类目分页查询参数（对应API的PagePrProductionBudgetItemsDto）
export interface IPagePrProductionBudgetItemsDto {
  pageIndex: number // 页码
  pageSize: number // 每页数量
  productionId: number // 项目ID
  itemName?: string // 打包名称（可选）
}

// 保存预算类目参数（对应API的SavePrProductionBudgetItemsDto）
export interface ISavePrProductionBudgetItemsDto {
  productionId: number // 项目ID
  budgetItems?: IPrProductionBudgetItem[] // 预算类目列表
}

// 预算类目列表分页响应
export interface IPrProductionBudgetItemsListResponse {
  list: IPrProductionBudgetItem[]
  total: number
  pageIndex: number
  pageSize: number
}

// 预算类目统计信息
export interface IBudgetCategorySummary {
  category: BudgetCategory // 一级分类
  categoryLabel: string // 一级分类标签
  totalAmount: number // 总金额
  itemCount: number // 项目数量
  personCount: number // 总人数
}

// 预算导入参数
export interface IImportBudgetParams {
  productionId: number // 项目ID
  file: File // 上传的文件
}

export interface IProjectDetailStore {
  /* API调用方法 */

  // 获取预算类目列表（分页）
  getBudgetItemsList: (params: IPagePrProductionBudgetItemsDto) => Promise<IPrProductionBudgetItemsListResponse | null>

  // 根据ID获取预算类目详情
  getBudgetItemById: (id: number) => Promise<IPrProductionBudgetItem | null>

  // 保存预算类目信息
  saveBudgetItems: (params: ISavePrProductionBudgetItemsDto) => Promise<boolean>

  // 删除预算类目
  deleteBudgetItem: (id: number) => Promise<boolean>

  // 根据项目ID获取所有预算类目（不分页）
  getAllBudgetItems: (productionId: number) => Promise<IPrProductionBudgetItem[] | null>

  // 保存单个预算类目
  saveSingleBudgetItem: (budgetItem: IPrProductionBudgetItem) => Promise<boolean>

  // 删除打包子类
  deleteItemsDetail: (id: number) => Promise<boolean>

  // 获取预算分类统计
  getBudgetCategorySummary: (productionId: number) => Promise<IBudgetCategorySummary[] | null>

  // 导入预算Excel/CSV
  importBudget: (params: IImportBudgetParams) => Promise<boolean>

  // 导出预算Excel
  exportBudget: (productionId: number) => string

  // 根据一级分类获取预算项目
  getBudgetItemsByCategory: (productionId: number, category: BudgetCategory) => Promise<IPrProductionBudgetItem[] | null>
}

export default create<IProjectDetailStore>(() => ({
  // 获取预算类目列表（分页）
  getBudgetItemsList: async (params: IPagePrProductionBudgetItemsDto) => {
    try {
      const { data, status } = await post<any, any>('/PrProductions/GetBudgetItemsList', params)

      if (status && data) {
        const list = Array.isArray(data?.dataList) ? data?.dataList : []

        return {
          list,
          total: data?.totalCount || 0,
          pageIndex: params.pageIndex,
          pageSize: params.pageSize,
        }
      }

      return null
    } catch (error) {
      console.error('获取预算类目列表失败:', error)

      return null
    }
  },

  // 根据ID获取预算类目详情
  getBudgetItemById: async (id: number) => {
    try {
      const { data, status } = await requestGet<any, any>(`/PrProductions/GetBudgetItemById?id=${id}`)

      if (status && data) {
        return data?.dataList || null
      }

      return null
    } catch (error) {
      console.error('获取预算类目详情失败:', error)

      return null
    }
  },

  // 保存预算类目信息
  saveBudgetItems: async (params: ISavePrProductionBudgetItemsDto) => {
    try {
      const { status } = await post<any, any>('/PrProductions/SaveBudgetItems', params)

      return !!status
    } catch (error) {
      console.error('保存预算类目信息失败:', error)

      return false
    }
  },

  // 删除预算类目
  deleteBudgetItem: async (id: number) => {
    try {
      const { status } = await requestGet<any, any>(`/PrProductions/DeleteBudgetItem?id=${id}`)

      return !!status
    } catch (error) {
      console.error('删除预算类目失败:', error)

      return false
    }
  },

  // 根据项目ID获取所有预算类目（不分页）
  getAllBudgetItems: async (productionId: number) => {
    try {
      const { data, status } = await requestGet<any, any>(`/PrProductions/GetAllBudgetItems?productionId=${productionId}`)

      if (status && data?.dataList) {
        const arr: IPrProductionBudgetItem[] = Array.isArray(data.dataList) ? data.dataList : [data.dataList]

        return arr
      }

      return null
    } catch (error) {
      console.error('获取所有预算类目失败:', error)

      return null
    }
  },

  // 保存单个预算类目
  saveSingleBudgetItem: async (budgetItem: IPrProductionBudgetItem) => {
    try {
      const { status } = await post<any, any>('/PrProductions/SaveSingleBudgetItem', budgetItem)

      return !!status
    } catch (error) {
      console.error('保存单个预算类目失败:', error)

      return false
    }
  },

  // 删除打包子类
  deleteItemsDetail: async (id: number) => {
    try {
      const { status } = await requestGet<any, any>(`/PrProductions/DeleteItemsDetail?id=${id}`)

      return !!status
    } catch (error) {
      console.error('删除打包子类失败:', error)

      return false
    }
  },

  // 获取预算分类统计
  getBudgetCategorySummary: async (productionId: number) => {
    try {
      const { data, status } = await requestGet<any, any>(`/PrProductions/GetBudgetCategorySummary?productionId=${productionId}`)

      if (status && data?.dataList) {
        return Array.isArray(data.dataList) ? data.dataList : []
      }

      return null
    } catch (error) {
      console.error('获取预算分类统计失败:', error)

      return null
    }
  },

  // 导入预算Excel/CSV
  importBudget: async (params: IImportBudgetParams) => {
    try {
      const formData = new FormData()
      formData.append('file', params.file)

      const { status } = await post<any, any>(
        `/PrProductions/ImportBudget?productionId=${params.productionId}`,
        formData,
        { headers: { 'Content-Type': 'multipart/form-data' } }
      )

      return !!status
    } catch (error) {
      console.error('导入预算失败:', error)

      return false
    }
  },

  // 导出预算Excel
  exportBudget: (productionId: number) => {
    return `${envUrl}/PrProductions/ExportBudget?productionId=${productionId}`
  },

  // 根据一级分类获取预算项目
  getBudgetItemsByCategory: async (productionId: number, category: BudgetCategory) => {
    try {
      const { data, status } = await requestGet<any, any>(
        `/PrProductions/GetBudgetItemsByCategory?productionId=${productionId}&category=${category}`
      )

      if (status && data?.dataList) {
        return Array.isArray(data.dataList) ? data.dataList : []
      }

      return null
    } catch (error) {
      console.error('根据分类获取预算项目失败:', error)

      return null
    }
  },
}))