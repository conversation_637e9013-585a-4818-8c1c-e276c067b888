# 建表sql
CREATE TABLE `pr_production_budget_items` (
  `Id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `ProductionId` int NOT NULL COMMENT '关联项目ID ',
  `IsPackage` VARCHAR(255) NULL COMMENT '是否打包',
  `ItemName` VARCHAR(255) NULL COMMENT '打包名称',
  `QuotedPrice` decimal(10,2) DEFAULT NULL COMMENT '单价',
  `PersonCount` int NOT NULL COMMENT '人数',
  `DayCount` int NOT NULL COMMENT '拍摄天数',
  `TotalPrice` decimal(18,2) DEFAULT NULL COMMENT '总金额',
  `HasInvoice` tinyint DEFAULT NULL COMMENT '是否正规发票（1=是，0=否）',
  `Description` text DEFAULT NULL COMMENT '备注说明',
  `CreateTime` datetime NOT NULL COMMENT '创建时间',
  `UpdateTime` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`),
  KEY `idx_productionid` (`ProductionId`)
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin AUTO_INCREMENT=180001 COMMENT='预算类目';


CREATE TABLE `pr_production_items_detail` (
  `Id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `ItemId` int NOT NULL COMMENT '打包项Id',
  `Category` int NOT NULL COMMENT '一级分类',
  `Subcategory` int NOT NULL COMMENT '二级分类',
  `PersonCount` int NOT NULL COMMENT '人数',
  `CreateTime` datetime NOT NULL COMMENT '创建时间',
  `UpdateTime` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`),
  KEY `idx_productionid` (`ItemId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin AUTO_INCREMENT=180001 COMMENT='打包子类 ';
# 后端枚举
1 制片
{"现场制片", 101},{"外联制片", 102},{"外联制片", 103},{"统筹", 104},{"场务", 105}
2 导演
{"导演", 201},{"执行导演", 202},{"场记", 203},{"武术指导", 204},{"演员副导", 205}
3 摄影
{"摄影师A", 301},{"摄影师B", 302},{"摄影师C", 303},{"摄影助理", 304},{"剧照", 305}
4 摄影器材
{"摄影器材", 401}
5 收音
{"收音师", 501},{"收音助理", 502}
6 后期
{"DIT", 601},{"配音", 602},{"特效", 603},{"剪辑", 604}
7 灯光
{"灯光师", 701},{"灯光助理", 702},{"灯光大助", 703}，{"灯光器材", 704}
8 美术组
{"美术师", 801},{"道具", 802},{"陈设", 803}
9 服化组
{"服装师", 901},{"化妆师", 902},{"服装采买/租赁", 903}
10 演员
{"女主", 1001},{"女二", 1002},{"男主", 1003},{"男二", 1004},{"女反", 1005},{"男反", 1006},{"女反二", 1007},{"男反二", 1008},{"跟组演员", 1009},{"特约演员", 1010},{"配角", 1011},{"特殊演员", 1012},{"群演", 1013},{"武行", 1014}
11 小计
{1101, "筹备期费用"},{1102, "车辆租赁"},{1103, "燃油费"},{1104, "戏用车辆"},{1105, "开机"},{1106, "场务消耗"},{1107, "差旅费"},{1108, "差旅费"},{1109, "保险费"},{1110, "餐费"},{1111, "片场风险控制预算"},{1112, "住宿费"}
12场租
{"场租", 1201}

# excel csv
项目,,人数,费用单价,拍摄天数（用工周期）,费用金额（元）,有无合同,备注,
场地费用小计,,,,,,,,
场租,,,,,,,预估,
人工费用小计,,,,,0,,,
制片,现场制片,,,,,,,
,外联制片,,,,,,,
,统筹,,,,,,,
,生活制片,,,,,,,
,场务,,,,,,,
导演,导演,,,,,,,
,执行导演,,,,,,,
,场记,,,,,,,
,武术指导,,,,,,,
,演员副导,,,,,,,
摄影,摄影师A,,,,,,,
,摄影师B,,,,,,,
,摄影师C,,,,,,,
,摄影助理,,,,,,,
,剧照,,,,,,,
摄影器材,,,,,,,,
收音,收音师,,,,,,,
,收音助理,,,,,,,
后期,DIT,,,,,,,
,配音,,,,,,,
,特效,,,,,,,
,剪辑,,,,,,,
灯光,灯光师,,,,,,,
,灯光大助,,,,,,,
,灯光师助理,,,,,,,
,灯光器材,,,,,,,
美术组,美术,,,,,,,
,道具,,,,,,,
,陈设,,,,,,,
服化组,服装师,,,,,,,
,化妆师,,,,,,,
,服装采买/租赁,,,,,,,
演员,演员1（女主）,,,,,,,
,演员2（男主）,,,,,,,
,演员3（女二）,,,,,,,
,演员4（男二）,,,,,,,
,演员5（女反）,,,,,,,
,演员6（男反）,,,,,,,
,演员7（女反二）,,,,,,,
,演员8（男反二）,,,,,,,
,武行,,,,,,,
,配角,,,,,,,
,特约,,,,,,,
,跟组演员,,,,,,,
,特殊演员,,,,,,,
,群演,,,,,,,
制片费小计,,,,,0,,,
筹备期费用,,,,,,,,
车辆租赁 ,,,,,,,,
燃油费,,,,,,,,
戏用车辆,,,,,,,,
开机,,,,,,,,
场务消耗,,,,,,,,
差旅费,,,,,,,,
剧杂费,,,,,,,,
保险费,,,,,,,,
餐费,,,,,,,,
片场风险控制预算,,,,,,,,
住宿费,,,,,,,,
预算总金额总计（不含税）,,,零元整,,,,0,
预算总金额总计（含税点3）,,,零元整,,,,0,
