CREATE TABLE `pr_production_budget_items` (
  `Id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `ProductionId` int NOT NULL COMMENT '关联项目ID ',
  `IsPackage` VARCHAR(255) NULL COMMENT '是否打包',
  `ItemName` VARCHAR(255) NULL COMMENT '打包名称',
  `QuotedPrice` decimal(10,2) DEFAULT NULL COMMENT '单价',
  `PersonCount` int NOT NULL COMMENT '人数',
  `DayCount` int NOT NULL COMMENT '拍摄天数',
  `TotalPrice` decimal(18,2) DEFAULT NULL COMMENT '总金额',
  `HasInvoice` tinyint DEFAULT NULL COMMENT '是否正规发票（1=是，0=否）',
  `Description` text DEFAULT NULL COMMENT '备注说明',
  `CreateTime` datetime NOT NULL COMMENT '创建时间',
  `UpdateTime` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`),
  KEY `idx_productionid` (`ProductionId`)
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin AUTO_INCREMENT=180001 COMMENT='预算类目';


CREATE TABLE `pr_production_items_detail` (
  `Id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `ItemId` int NOT NULL COMMENT '打包项Id',
  `Category` int NOT NULL COMMENT '一级分类',
  `Subcategory` int NOT NULL COMMENT '二级分类',
  `PersonCount` int NOT NULL COMMENT '人数',
  `CreateTime` datetime NOT NULL COMMENT '创建时间',
  `UpdateTime` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`),
  KEY `idx_productionid` (`ItemId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin AUTO_INCREMENT=180001 COMMENT='打包子类 ';